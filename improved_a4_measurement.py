"""
Optimised A4‑paper measurement and shape recognition using a central ROI.

该脚本在提供的 a4_measurement_with_roi (1).py 基础上进行了若干优化，用于识别黑色线条绘制的图形并测量距离和尺寸。主要优化包括：
1. 灰度图高斯模糊 + 自适应阈值，提高对暗线条的提取能力。
2. 开运算加闭运算，填补线条间隙，保证轮廓闭合。
3. 仅检索外轮廓，减少嵌套干扰。
4. 可选多边形过滤，可跳过 5 边、6 边等噪声形状。
脚本在检测到 A4 纸后，计算像素到实际的比例并用标定常数求出距离 D；在纸内检测形状后，计算其实际尺寸 X，并在屏幕左上角显示 D 和 X。
"""

from maix import image, display, app, camera
import cv2
import numpy as np

# A4 纸的物理尺寸（单位：厘米）
A4_WIDTH  = 21.0
A4_HEIGHT = 29.7

# 标定常数：D_ref × w_ref，用于通过 A4 宽度求距离 D
REFERENCE_DISTANCE_CM  = 100.0   # 标定时 A4 与相机的距离（厘米）
REFERENCE_WIDTH_PIXELS = 200.0   # 标定时 A4 在图像中的像素宽度
CALIBRATION_CONSTANT   = REFERENCE_DISTANCE_CM * REFERENCE_WIDTH_PIXELS

# ROI 区域（根据相机分辨率 320×240 适当调整）
ROI_X, ROI_Y = 128, 128
ROI_W, ROI_H = 64, 64

# 初始化显示和摄像头
disp = display.Display()
cam  = camera.Camera(320, 240, image.Format.FMT_BGR888)

def detect_shape(contour: np.ndarray):
    """根据轮廓近似多边形判断形状。返回形状名称和近似顶点。"""
    perimeter = cv2.arcLength(contour, True)
    approx    = cv2.approxPolyDP(contour, 0.03 * perimeter, True)
    vertices  = len(approx)
    if vertices == 3:
        return "triangle", approx
    elif vertices == 4:
        x, y, w, h = cv2.boundingRect(approx)
        aspect_ratio = float(w) / h
        return ("square" if 0.9 <= aspect_ratio <= 1.1 else "rectangle"), approx
    elif vertices >= 8:
        return "circle", approx
    return f"{vertices}-side", approx

def main():
    print("Optimised ROI‑restricted measurement system initialised…")
    measuring = False
    measurement_results = {}
    # 当前显示的距离（cm）和尺寸（cm）
    current_distance_cm = None
    current_size_cm     = None
    frame_count = 0

    while not app.need_exit():
        frame_count += 1
        img = cam.read()
        img_cv = image.image2cv(img, ensure_bgr=False, copy=False)

        # 在原图上绘制 ROI 边框
        img_display = img_cv.copy()
        cv2.rectangle(img_display, (ROI_X, ROI_Y), (ROI_X + ROI_W, ROI_Y + ROI_H), (255, 0, 0), 1)

        # ROI 内图像处理
        roi_frame = img_cv[ROI_Y:ROI_Y + ROI_H, ROI_X:ROI_X + ROI_W]
        gray      = cv2.cvtColor(roi_frame, cv2.COLOR_BGR2GRAY)
        blurred   = cv2.GaussianBlur(gray, (5, 5), 0)
        # 自适应阈值（反阈值，突出黑线）
        thresh = cv2.adaptiveThreshold(
            blurred, 255,
            cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
            cv2.THRESH_BINARY_INV,
            11, 2
        )
        kernel = np.ones((3, 3), np.uint8)
        # 先开运算去噪，再闭运算连线
        thresh = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel)
        thresh = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 在 ROI 内查找最大的矩形或正方形边框作为 A4 纸
        border_rectangles = []
        for contour in contours:
            if cv2.contourArea(contour) < 500:
                continue
            shape_name, approx = detect_shape(contour)
            if shape_name in ("rectangle", "square"):
                border_rectangles.append(contour)

        if border_rectangles:
            # 取最大的矩形作为 A4 纸
            largest = max(border_rectangles, key=cv2.contourArea)
            x, y, w, h = cv2.boundingRect(largest)
            global_x, global_y = x + ROI_X, y + ROI_Y
            cv2.rectangle(img_display, (global_x, global_y),
                          (global_x + w, global_y + h), (0, 255, 0), 2)
            cv2.putText(img_display, "A4 Paper",
                        (global_x, global_y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5,
                        (0, 255, 0), 1)

            # 根据 A4 宽度计算像素比例和距离 D
            scale_x = A4_WIDTH / w
            scale_y = A4_HEIGHT / h
            scale   = (scale_x + scale_y) / 2.0
            cv2.putText(img_display, f"Scale: {scale:.4f} cm/pixel",
                        (10, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            distance_cm = CALIBRATION_CONSTANT / w
            distance_m  = distance_cm / 100.0
            cv2.putText(img_display, f"Distance: {distance_m:.2f} m",
                        (10, 40), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

            # 模拟按键：每 100 帧进行一次测量
            if frame_count % 100 == 0:
                measuring = True
            if measuring:
                # 仅在 A4 区域内继续处理图形
                mask = np.zeros_like(gray)
                cv2.rectangle(mask, (x, y), (x + w, y + h), 255, -1)
                masked = cv2.bitwise_and(thresh, thresh, mask=mask)
                # 额外闭运算连接线条
                masked = cv2.morphologyEx(masked, cv2.MORPH_CLOSE, kernel)
                targets, _ = cv2.findContours(masked, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                current_distance_cm = distance_cm
                current_size_cm     = None
                for contour in targets:
                    area = cv2.contourArea(contour)
                    if area < 100 or area > (w * h * 0.5):
                        continue
                    shape_name, approx = detect_shape(contour)
                    # 若想过滤 5 边、6 边等噪声可取消下面注释
                    # if shape_name.endswith("-side"):
                    #     continue
                    # 全局绘制
                    cv2.drawContours(img_display, [contour + np.array([[ROI_X, ROI_Y]])], -1,
                                     (0, 0, 255), 2)
                    M = cv2.moments(contour)
                    if M["m00"] == 0:
                        continue
                    cx = int(M["m10"] / M["m00"]) + ROI_X
                    cy = int(M["m01"] / M["m00"]) + ROI_Y
                    cv2.putText(img_display, shape_name,
                                (cx - 20, cy), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
                    cv2.circle(img_display, (cx, cy), 3, (0, 0, 255), -1)
                    # 计算尺寸
                    _, _, w_obj, h_obj = cv2.boundingRect(contour)
                    width_cm  = w_obj * scale
                    height_cm = h_obj * scale
                    size_cm   = None
                    area_cm2  = None
                    if shape_name == "circle":
                        diameter_pixels = np.sqrt(4 * area / np.pi)
                        size_cm = diameter_pixels * scale
                    elif shape_name in ("square", "rectangle"):
                        area_cm2 = (w_obj * scale) * (h_obj * scale)
                        size_cm  = max(width_cm, height_cm)
                    elif shape_name == "triangle":
                        size_cm = height_cm
                    else:
                        size_cm = max(width_cm, height_cm)
                    # 更新当前尺寸 X
                    if size_cm is not None:
                        current_size_cm = size_cm
                    # 显示每个图形的尺寸和距离
                    if area_cm2 is not None:
                        cv2.putText(img_display, f"Area: {area_cm2:.1f} cm^2",
                                    (cx - 50, cy + 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5,
                                    (0, 0, 255), 1)
                    else:
                        cv2.putText(img_display, f"Size: {size_cm:.1f} cm",
                                    (cx - 50, cy + 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5,
                                    (0, 0, 255), 1)
                    cv2.putText(img_display, f"D: {distance_m:.2f} m",
                                (cx - 50, cy + 40), cv2.FONT_HERSHEY_SIMPLEX, 0.5,
                                (0, 0, 255), 1)
                measuring = False

        # 如果获得有效测量，则在左上角显示 D 和 X（厘米）
        if current_distance_cm is not None and current_size_cm is not None:
            cv2.putText(img_display, f"D: {current_distance_cm:.1f} cm",
                        (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
            cv2.putText(img_display, f"X: {current_size_cm:.1f} cm",
                        (10, 80), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)

        # 显示结果
        img_show = image.cv2image(img_display, bgr=True, copy=False)
        disp.show(img_show)

    print("Exiting optimised measurement system.")

if __name__ == "__main__":
    main()
