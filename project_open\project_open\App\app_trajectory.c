#include "app_trajectory.h"
#include <math.h>

// 全局变量定义
trajectory_rect_t pencil_trajectory;       // 铅笔框轨迹
trajectory_rect_t vector_trajectory;       // 矩形框轨迹
trajectory_polyline_t polyline_trajectory; // 多点轨迹
MultiTimer mt_pencil_trajectory;           // 铅笔框定时器
MultiTimer mt_vector_trajectory;           // 矩形框定时器

/**
 * @brief 轨迹控制初始化
 */
void app_trajectory_init(void)
{
    // 初始化铅笔框轨迹结构体
    memset(&pencil_trajectory, 0, sizeof(pencil_trajectory));
    pencil_trajectory.trajectory_running = 0;
    pencil_trajectory.move_to_start = 0;
    pencil_trajectory.is_resetting = 0;
    pencil_trajectory.is_returning = 0;
    pencil_trajectory.total_steps_per_edge = 80; // 每条边80个步长

    // 初始化矩形框轨迹结构体
    memset(&vector_trajectory, 0, sizeof(vector_trajectory));
    vector_trajectory.trajectory_running = 0;
    vector_trajectory.move_to_start = 0;
    vector_trajectory.is_resetting = 0;
    vector_trajectory.is_returning = 0;
    vector_trajectory.total_steps_per_edge = 100; // 每条边100个步长

    // 初始化多点轨迹结构体
    memset(&polyline_trajectory, 0, sizeof(polyline_trajectory));
    polyline_trajectory.trajectory_running = 0;
    polyline_trajectory.move_to_start = 0;
    polyline_trajectory.is_resetting = 0;
    polyline_trajectory.is_returning = 0;
    polyline_trajectory.total_steps_per_segment = 30; // 每段30个步长
    polyline_trajectory.last_target.x = 0;            // 初始化平滑变量
    polyline_trajectory.last_target.y = 0;
}

/**
 * @brief 设置铅笔框轨迹参数（简单线性插值）
 */
void app_pencil_set_rect(int x1, int y1, int x2, int y2, int x3, int y3, int x4, int y4)
{
    pencil_trajectory.corners[0].x = x1;
    pencil_trajectory.corners[0].y = y1;
    pencil_trajectory.corners[1].x = x2;
    pencil_trajectory.corners[1].y = y2;
    pencil_trajectory.corners[2].x = x3;
    pencil_trajectory.corners[2].y = y3;
    pencil_trajectory.corners[3].x = x4;
    pencil_trajectory.corners[3].y = y4;
    text01[0] = x1;

    my_printf(&huart1, "Pencil Rect Set: (%d,%d)->(%d,%d)->(%d,%d)->(%d,%d)\r\n",
              x1, y1, x2, y2, x3, y3, x4, y4);
}

/**
 * @brief 开始铅笔框轨迹运动
 */
void app_pencil_start(void)
{
    // 如果是第一次启动或者已经完成一轮循环，重置到起点
    if (pencil_trajectory.current_edge == 0 && pencil_trajectory.current_step == 0)
    {
        pencil_trajectory.move_to_start = 1; // 需要移动到起点
        app_pid_set_target(pencil_trajectory.corners[0].x, pencil_trajectory.corners[0].y);
        my_printf(&huart1, "Pencil Start, Move to Start Point(%d,%d)\r\n",
                  pencil_trajectory.corners[0].x, pencil_trajectory.corners[0].y);
    }
    else
    {
        // 从当前位置继续
        pencil_trajectory.move_to_start = 0;
        trajectory_point_t current_target = app_pencil_calc_next_point();
        app_pid_set_target(current_target.x, current_target.y);
        my_printf(&huart1, "Pencil Continue from Edge%d Step%d\r\n",
                  pencil_trajectory.current_edge, pencil_trajectory.current_step);
    }

    pencil_trajectory.trajectory_running = 1;

    // 启动轨迹定时器
    multiTimerStart(&mt_pencil_trajectory, TRAJECTORY_UPDATE_PERIOD, app_pencil_task, NULL);
}

/**
 * @brief 停止铅笔框轨迹运动
 */
void app_pencil_stop(void)
{
    pencil_trajectory.trajectory_running = 0;
    multiTimerStop(&mt_pencil_trajectory);

    // 保存当前状态，以便下次继续
    my_printf(&huart1, "Pencil Stop at Edge%d Step%d\r\n",
              pencil_trajectory.current_edge, pencil_trajectory.current_step);
}

/**
 * @brief 重新开始铅笔框轨迹运动（从起点开始）
 */
void app_pencil_restart(void)
{
    // 重置到起点
    pencil_trajectory.current_edge = 0;
    pencil_trajectory.current_step = 0;

    // 开始运动
    app_pencil_start();

    my_printf(&huart1, "Pencil Restart from Beginning\r\n");
}

/**
 * @brief 复位到铅笔框中心
 */
void app_pencil_reset_to_center(void)
{
    // 检查是否已设置铅笔框角点（判断是否有有效的中心点）
    if (pencil_trajectory.corners[0].x == 0 && pencil_trajectory.corners[0].y == 0 &&
        pencil_trajectory.corners[1].x == 0 && pencil_trajectory.corners[1].y == 0 &&
        pencil_trajectory.corners[2].x == 0 && pencil_trajectory.corners[2].y == 0 &&
        pencil_trajectory.corners[3].x == 0 && pencil_trajectory.corners[3].y == 0)
    {
        my_printf(&huart1, "Pencil Corners Not Set, Cannot Reset\r\n");
        return;
    }

    // 如果正在运行轨迹，保存当前状态
    if (pencil_trajectory.trajectory_running)
    {
        pencil_trajectory.saved_position = app_pencil_calc_next_point(); // 保存当前轨迹位置
        pencil_trajectory.saved_edge = pencil_trajectory.current_edge;
        pencil_trajectory.saved_step = pencil_trajectory.current_step;
    }

    // 设置复位状态
    pencil_trajectory.is_resetting = 1;
    pencil_trajectory.is_returning = 0;

    // 如果轨迹没有运行，启动定时器来处理复位
    if (!pencil_trajectory.trajectory_running)
    {
        multiTimerStart(&mt_pencil_trajectory, TRAJECTORY_UPDATE_PERIOD, app_pencil_task, NULL);
    }

    // 计算中心点并设置为目标
    trajectory_point_t center = calc_pencil_center();
    app_pid_set_target(center.x, center.y);

    my_printf(&huart1, "Start Reset to Pencil Center(%d,%d)\r\n", center.x, center.y);
}

/**
 * @brief 返回原位置并继续循迹
 */
void app_pencil_return_and_continue(void)
{
    if (!pencil_trajectory.is_resetting)
    {
        my_printf(&huart1, "Not in Reset State, Cannot Return\r\n");
        return;
    }

    // 检查是否有保存的位置（即之前是从运行状态复位的）
    if (pencil_trajectory.saved_position.x == 0 && pencil_trajectory.saved_position.y == 0)
    {
        my_printf(&huart1, "No Saved Position, Cannot Return\r\n");
        return;
    }

    // 设置返回状态
    pencil_trajectory.is_returning = 1;
    pencil_trajectory.is_resetting = 0;

    // 恢复保存的状态
    pencil_trajectory.current_edge = pencil_trajectory.saved_edge;
    pencil_trajectory.current_step = pencil_trajectory.saved_step;

    // 如果之前是运行状态，恢复运行状态
    if (!pencil_trajectory.trajectory_running)
    {
        pencil_trajectory.trajectory_running = 1;
        // 启动轨迹定时器
        multiTimerStart(&mt_pencil_trajectory, TRAJECTORY_UPDATE_PERIOD, app_pencil_task, NULL);
    }

    // 设置目标为保存的位置
    app_pid_set_target(pencil_trajectory.saved_position.x, pencil_trajectory.saved_position.y);

    my_printf(&huart1, "Return to Position(%d,%d) and Continue\r\n",
              pencil_trajectory.saved_position.x, pencil_trajectory.saved_position.y);
}

/**
 * @brief 计算边长
 */
float calc_edge_length(trajectory_point_t start, trajectory_point_t end)
{
    float dx = end.x - start.x;
    float dy = end.y - start.y;
    return sqrt(dx * dx + dy * dy);
}

/**
 * @brief 计算铅笔框中心点
 */
trajectory_point_t calc_pencil_center(void)
{
    trajectory_point_t center;

    // 计算四个角点的平均值作为中心点
    center.x = (pencil_trajectory.corners[0].x + pencil_trajectory.corners[1].x +
                pencil_trajectory.corners[2].x + pencil_trajectory.corners[3].x) /
               4;
    center.y = (pencil_trajectory.corners[0].y + pencil_trajectory.corners[1].y +
                pencil_trajectory.corners[2].y + pencil_trajectory.corners[3].y) /
               4;

    return center;
}

/**
 * @brief 计算矩形框中心点
 */
trajectory_point_t calc_vector_center(void)
{
    trajectory_point_t center;

    // 计算四个角点的平均值作为中心点
    center.x = (vector_trajectory.corners[0].x + vector_trajectory.corners[1].x +
                vector_trajectory.corners[2].x + vector_trajectory.corners[3].x) /
               4;
    center.y = (vector_trajectory.corners[0].y + vector_trajectory.corners[1].y +
                vector_trajectory.corners[2].y + vector_trajectory.corners[3].y) /
               4;

    return center;
}

/**
 * @brief 计算铅笔框下一个轨迹点（简单线性插值）
 */
trajectory_point_t app_pencil_calc_next_point(void)
{
    trajectory_point_t next_point;

    // 获取当前边的起点和终点
    int start_idx = pencil_trajectory.current_edge;
    int end_idx = (pencil_trajectory.current_edge + 1) % 4;

    trajectory_point_t start = pencil_trajectory.corners[start_idx];
    trajectory_point_t end = pencil_trajectory.corners[end_idx];

    // 简单线性插补计算
    float ratio = (float)pencil_trajectory.current_step / (float)pencil_trajectory.total_steps_per_edge;

    next_point.x = start.x + (int)((end.x - start.x) * ratio);
    next_point.y = start.y + (int)((end.y - start.y) * ratio);

    return next_point;
}

/**
 * @brief 计算矩形框下一个轨迹点（向量参数化插值）
 */
trajectory_point_t app_vector_calc_next_point(void)
{
    trajectory_point_t next_point;

    // 获取当前边的起点和终点
    int start_idx = vector_trajectory.current_edge;
    int end_idx = (vector_trajectory.current_edge + 1) % 4;

    trajectory_point_t start = vector_trajectory.corners[start_idx];
    trajectory_point_t end = vector_trajectory.corners[end_idx];

    // 计算边长
    float edge_length = calc_edge_length(start, end);

    // 计算单位向量
    float dx = end.x - start.x;
    float dy = end.y - start.y;

    if (edge_length > 0.1f) // 避免除零
    {
        float unit_x = dx / edge_length;
        float unit_y = dy / edge_length;

        // 计算步长（每条边按实际长度等分）
        float step_length = edge_length / (float)vector_trajectory.total_steps_per_edge;
        float current_distance = vector_trajectory.current_step * step_length;

        // 向量参数化插值
        next_point.x = start.x + (int)(current_distance * unit_x);
        next_point.y = start.y + (int)(current_distance * unit_y);
    }
    else
    {
        // 边长太短，直接返回起点
        next_point = start;
    }

    return next_point;
}

/**
 * @brief 检查是否到达目标点
 */
uint8_t app_trajectory_check_arrival(trajectory_point_t target, trajectory_point_t current)
{
    float dx = target.x - current.x;
    float dy = target.y - current.y;
    float distance = sqrt(dx * dx + dy * dy);

    return (distance <= TRAJECTORY_ARRIVE_THRESHOLD);
}

/**
 * @brief 铅笔框轨迹任务函数
 */
void app_pencil_task(MultiTimer *timer, void *userData)
{
    // 如果既不在运行状态，也不在复位/返回状态，则退出
    if (!pencil_trajectory.trajectory_running &&
        !pencil_trajectory.is_resetting &&
        !pencil_trajectory.is_returning)
        return;

    // 如果正在移动到起点
    if (pencil_trajectory.move_to_start)
    {
        // 检查是否已经到达起点（这里简化处理，实际可以检查当前位置）
        // 假设经过几个周期后就到达了起点
        static uint8_t start_wait_count = 0;
        start_wait_count++;

        // 在等待期间持续设置目标为第一个角点
        app_pid_set_target(pencil_trajectory.corners[0].x, pencil_trajectory.corners[0].y);

        if (start_wait_count >= 20) // 等待1000ms (20 * 50ms)，增加等待时间
        {
            start_wait_count = 0;
            pencil_trajectory.move_to_start = 0; // 清除移动到起点标志
            my_printf(&huart1, "Reached Start Point, Begin Pencil Motion\r\n");
        }
        else
        {
            my_printf(&huart1, "Moving to Start... %d/20\r\n", start_wait_count);
        }
    }
    // 如果正在复位到中心
    else if (pencil_trajectory.is_resetting)
    {
        static uint8_t reset_wait_count = 0;
        reset_wait_count++;

        // 持续设置目标为中心点
        trajectory_point_t center = calc_pencil_center();
        app_pid_set_target(center.x, center.y);

        if (reset_wait_count >= 15) // 等待750ms到达中心
        {
            reset_wait_count = 0;
            my_printf(&huart1, "Reached Center, Wait for Return Command\r\n");

            // 如果原来没有运行轨迹，复位完成后停止定时器
            if (!pencil_trajectory.trajectory_running)
            {
                // 不停止定时器，保持运行以便处理返回命令
                // multiTimerStop(&mt_pencil_trajectory);
            }
        }
        else
        {
            my_printf(&huart1, "Resetting to Center... %d/15\r\n", reset_wait_count);
        }
    }
    // 如果正在返回原位置
    else if (pencil_trajectory.is_returning)
    {
        static uint8_t return_wait_count = 0;
        return_wait_count++;

        // 持续设置目标为保存的位置
        app_pid_set_target(pencil_trajectory.saved_position.x, pencil_trajectory.saved_position.y);

        if (return_wait_count >= 15) // 等待750ms到达原位置
        {
            return_wait_count = 0;
            pencil_trajectory.is_returning = 0; // 清除返回标志
            my_printf(&huart1, "Returned to Position, Continue Trajectory\r\n");
        }
        else
        {
            my_printf(&huart1, "Returning to Position... %d/15\r\n", return_wait_count);
        }
    }
    else
    {
        // 正常轨迹运动
        trajectory_point_t target = app_pencil_calc_next_point();

        // 获取摄像头识别的当前位置
        extern int current_x, current_y;
        trajectory_point_t current = {current_x, current_y};

        // 检查是否到达目标点
        if (app_trajectory_check_arrival(target, current))
        {
            my_printf(&huart1, "Pencil Arrived at Target: X=%d Y=%d\r\n", target.x, target.y);

            // 更新步数
            pencil_trajectory.current_step++;

            // 检查是否完成当前边
            if (pencil_trajectory.current_step >= pencil_trajectory.total_steps_per_edge)
            {
                pencil_trajectory.current_step = 0;
                pencil_trajectory.current_edge = (pencil_trajectory.current_edge + 1) % 4;

                my_printf(&huart1, "Switch to Edge%d\r\n", pencil_trajectory.current_edge);
            }
        }
        else
        {
            // 未到达目标点，继续设置目标
            app_pid_set_target(target.x, target.y);
            my_printf(&huart1, "Pencil Target: Edge%d Step%d X=%d Y=%d (Current: X=%d Y=%d)\r\n",
                      pencil_trajectory.current_edge, pencil_trajectory.current_step,
                      target.x, target.y, current.x, current.y);
        }
    }

    // 重新启动定时器
    multiTimerStart(&mt_pencil_trajectory, TRAJECTORY_UPDATE_PERIOD, app_pencil_task, NULL);
}

// ==================== 矩形框循迹（向量参数化插值）====================

/**
 * @brief 设置矩形框轨迹参数（向量参数化插值）
 */
void app_vector_set_rect(int x1, int y1, int x2, int y2, int x3, int y3, int x4, int y4)
{
    vector_trajectory.corners[0].x = x1;
    vector_trajectory.corners[0].y = y1;
    vector_trajectory.corners[1].x = x2;
    vector_trajectory.corners[1].y = y2;
    vector_trajectory.corners[2].x = x3;
    vector_trajectory.corners[2].y = y3;
    vector_trajectory.corners[3].x = x4;
    vector_trajectory.corners[3].y = y4;

    my_printf(&huart1, "Vector Rect Set: (%d,%d)->(%d,%d)->(%d,%d)->(%d,%d)\r\n",
              x1, y1, x2, y2, x3, y3, x4, y4);
}

/**
 * @brief 开始矩形框轨迹运动
 */
void app_vector_start(void)
{
    // 如果是第一次启动或者已经完成一轮循环，重置到起点
    if (vector_trajectory.current_edge == 0 && vector_trajectory.current_step == 0)
    {
        vector_trajectory.move_to_start = 1; // 需要移动到起点
        app_pid_set_target(vector_trajectory.corners[0].x, vector_trajectory.corners[0].y);
        my_printf(&huart1, "Vector Start, Move to Start Point(%d,%d)\r\n",
                  vector_trajectory.corners[0].x, vector_trajectory.corners[0].y);
    }
    else
    {
        // 从当前位置继续
        vector_trajectory.move_to_start = 0;
        trajectory_point_t current_target = app_vector_calc_next_point();
        app_pid_set_target(current_target.x, current_target.y);
        my_printf(&huart1, "Vector Continue from Edge%d Step%d\r\n",
                  vector_trajectory.current_edge, vector_trajectory.current_step);
    }

    vector_trajectory.trajectory_running = 1;

    // 启动轨迹定时器
    multiTimerStart(&mt_vector_trajectory, TRAJECTORY_UPDATE_PERIOD, app_vector_task, NULL);
}

/**
 * @brief 停止矩形框轨迹运动
 */
void app_vector_stop(void)
{
    vector_trajectory.trajectory_running = 0;
    multiTimerStop(&mt_vector_trajectory);

    // 保存当前状态，以便下次继续
    my_printf(&huart1, "Vector Stop at Edge%d Step%d\r\n",
              vector_trajectory.current_edge, vector_trajectory.current_step);
}

/**
 * @brief 重新开始矩形框轨迹运动（从起点开始）
 */
void app_vector_restart(void)
{
    // 重置到起点
    vector_trajectory.current_edge = 0;
    vector_trajectory.current_step = 0;

    // 开始运动
    app_vector_start();

    my_printf(&huart1, "Vector Restart from Beginning\r\n");
}

/**
 * @brief 复位到矩形框中心
 */
void app_vector_reset_to_center(void)
{
    // 检查是否已设置矩形框角点（判断是否有有效的中心点）
    if (vector_trajectory.corners[0].x == 0 && vector_trajectory.corners[0].y == 0 &&
        vector_trajectory.corners[1].x == 0 && vector_trajectory.corners[1].y == 0 &&
        vector_trajectory.corners[2].x == 0 && vector_trajectory.corners[2].y == 0 &&
        vector_trajectory.corners[3].x == 0 && vector_trajectory.corners[3].y == 0)
    {
        my_printf(&huart1, "Vector Corners Not Set, Cannot Reset\r\n");
        return;
    }

    // 如果正在运行轨迹，保存当前状态
    if (vector_trajectory.trajectory_running)
    {
        vector_trajectory.saved_position = app_vector_calc_next_point(); // 保存当前轨迹位置
        vector_trajectory.saved_edge = vector_trajectory.current_edge;
        vector_trajectory.saved_step = vector_trajectory.current_step;
    }

    // 设置复位状态
    vector_trajectory.is_resetting = 1;
    vector_trajectory.is_returning = 0;

    // 如果轨迹没有运行，启动定时器来处理复位
    if (!vector_trajectory.trajectory_running)
    {
        multiTimerStart(&mt_vector_trajectory, TRAJECTORY_UPDATE_PERIOD, app_vector_task, NULL);
    }

    // 计算中心点并设置为目标
    trajectory_point_t center = calc_vector_center();
    app_pid_set_target(center.x, center.y);

    my_printf(&huart1, "Start Reset to Vector Center(%d,%d)\r\n", center.x, center.y);
}

/**
 * @brief 返回原位置并继续循迹
 */
void app_vector_return_and_continue(void)
{
    if (!vector_trajectory.is_resetting)
    {
        my_printf(&huart1, "当前未处于复位状态，无法返回\r\n");
        return;
    }

    // 检查是否有保存的位置（即之前是从运行状态复位的）
    if (vector_trajectory.saved_position.x == 0 && vector_trajectory.saved_position.y == 0)
    {
        my_printf(&huart1, "无保存的位置信息，无法返回\r\n");
        return;
    }

    // 设置返回状态
    vector_trajectory.is_returning = 1;
    vector_trajectory.is_resetting = 0;

    // 恢复保存的状态
    vector_trajectory.current_edge = vector_trajectory.saved_edge;
    vector_trajectory.current_step = vector_trajectory.saved_step;

    // 如果之前是运行状态，恢复运行状态
    if (!vector_trajectory.trajectory_running)
    {
        vector_trajectory.trajectory_running = 1;
        // 启动轨迹定时器
        multiTimerStart(&mt_vector_trajectory, TRAJECTORY_UPDATE_PERIOD, app_vector_task, NULL);
    }

    // 设置目标为保存的位置
    app_pid_set_target(vector_trajectory.saved_position.x, vector_trajectory.saved_position.y);

    my_printf(&huart1, "Return to Position(%d,%d) and Continue\r\n",
              vector_trajectory.saved_position.x, vector_trajectory.saved_position.y);
}

/**
 * @brief 矩形框轨迹任务函数
 */
void app_vector_task(MultiTimer *timer, void *userData)
{
    // 如果既不在运行状态，也不在复位/返回状态，则退出
    if (!vector_trajectory.trajectory_running &&
        !vector_trajectory.is_resetting &&
        !vector_trajectory.is_returning)
        return;

    // 如果正在移动到起点
    if (vector_trajectory.move_to_start)
    {
        // 检查是否已经到达起点（这里简化处理，实际可以检查当前位置）
        // 假设经过几个周期后就到达了起点
        static uint8_t start_wait_count = 0;
        start_wait_count++;

        // 在等待期间持续设置目标为第一个角点
        app_pid_set_target(vector_trajectory.corners[0].x, vector_trajectory.corners[0].y);

        if (start_wait_count >= 20) // 等待1000ms (20 * 50ms)，增加等待时间
        {
            start_wait_count = 0;
            vector_trajectory.move_to_start = 0; // 清除移动到起点标志
            my_printf(&huart1, "Reached Start Point, Begin Vector Motion\r\n");
        }
        else
        {
            my_printf(&huart1, "Moving to Start... %d/20\r\n", start_wait_count);
        }
    }
    // 如果正在复位到中心
    else if (vector_trajectory.is_resetting)
    {
        static uint8_t reset_wait_count = 0;
        reset_wait_count++;

        // 持续设置目标为中心点
        trajectory_point_t center = calc_vector_center();
        app_pid_set_target(center.x, center.y);

        if (reset_wait_count >= 15) // 等待750ms到达中心
        {
            reset_wait_count = 0;
            my_printf(&huart1, "Reached Center, Wait for Return Command\r\n");

            // 如果原来没有运行轨迹，复位完成后停止定时器
            if (!vector_trajectory.trajectory_running)
            {
                // 不停止定时器，保持运行以便处理返回命令
                // multiTimerStop(&mt_vector_trajectory);
            }
        }
        else
        {
            my_printf(&huart1, "Resetting to Center... %d/15\r\n", reset_wait_count);
        }
    }
    // 如果正在返回原位置
    else if (vector_trajectory.is_returning)
    {
        static uint8_t return_wait_count = 0;
        return_wait_count++;

        // 持续设置目标为保存的位置
        app_pid_set_target(vector_trajectory.saved_position.x, vector_trajectory.saved_position.y);

        if (return_wait_count >= 15) // 等待750ms到达原位置
        {
            return_wait_count = 0;
            vector_trajectory.is_returning = 0; // 清除返回标志
            my_printf(&huart1, "Returned to Position, Continue Trajectory\r\n");
        }
        else
        {
            my_printf(&huart1, "Returning to Position... %d/15\r\n", return_wait_count);
        }
    }
    else
    {
        // 正常轨迹运动
        trajectory_point_t target = app_vector_calc_next_point();

        // 获取摄像头识别的当前位置
        extern int current_x, current_y;
        trajectory_point_t current = {current_x, current_y};

        // 检查是否到达目标点
        if (app_trajectory_check_arrival(target, current))
        {
            my_printf(&huart1, "Vector Arrived at Target: X=%d Y=%d\r\n", target.x, target.y);

            // 更新步数
            vector_trajectory.current_step++;

            // 检查是否完成当前边
            if (vector_trajectory.current_step >= vector_trajectory.total_steps_per_edge)
            {
                vector_trajectory.current_step = 0;
                vector_trajectory.current_edge = (vector_trajectory.current_edge + 1) % 4;

                my_printf(&huart1, "Switch to Edge%d\r\n", vector_trajectory.current_edge);
            }
        }
        else
        {
            // 未到达目标点，继续设置目标
            app_pid_set_target(target.x, target.y);
            my_printf(&huart1, "Vector Target: Edge%d Step%d X=%d Y=%d (Current: X=%d Y=%d)\r\n",
                      vector_trajectory.current_edge, vector_trajectory.current_step,
                      target.x, target.y, current.x, current.y);
        }
    }

    // 重新启动定时器
    multiTimerStart(&mt_vector_trajectory, TRAJECTORY_UPDATE_PERIOD, app_vector_task, NULL);
}

// ==================== 多点轨迹循迹（样条插值）====================

/**
 * @brief 设置多点轨迹参数
 */
void app_polyline_set_points(int *coords, int point_count)
{
    if (point_count > MAX_POLYLINE_POINTS)
    {
        point_count = MAX_POLYLINE_POINTS; // 限制最大点数
        my_printf(&huart1, "Point count limited to %d\r\n", MAX_POLYLINE_POINTS);
    }

    polyline_trajectory.point_count = point_count;
    for (int i = 0; i < point_count; i++)
    {
        polyline_trajectory.points[i].x = coords[i * 2];     // x坐标
        polyline_trajectory.points[i].y = coords[i * 2 + 1]; // y坐标
    }

    my_printf(&huart1, "Polyline Set: %d points\r\n", point_count);
    for (int i = 0; i < point_count; i++)
    {
        my_printf(&huart1, "P%d:(%d,%d) ", i, polyline_trajectory.points[i].x, polyline_trajectory.points[i].y);
    }
    my_printf(&huart1, "\r\n");
}

/**
 * @brief 开始多点轨迹运动
 */
void app_polyline_start(void)
{
    if (polyline_trajectory.point_count < 2)
    {
        my_printf(&huart1, "Polyline needs at least 2 points\r\n");
        return;
    }

    // 如果是第一次启动，重置到起点
    if (polyline_trajectory.current_segment == 0 && polyline_trajectory.current_step == 0)
    {
        polyline_trajectory.move_to_start = 1; // 需要移动到起点
        app_pid_set_target(polyline_trajectory.points[0].x, polyline_trajectory.points[0].y);
        my_printf(&huart1, "Polyline Start, Move to Start Point(%d,%d)\r\n",
                  polyline_trajectory.points[0].x, polyline_trajectory.points[0].y);
    }
    else
    {
        // 从当前位置继续
        polyline_trajectory.move_to_start = 0;
        trajectory_point_t current_target = app_polyline_calc_next_point();
        app_pid_set_target(current_target.x, current_target.y);
        my_printf(&huart1, "Polyline Continue from Segment%d Step%d\r\n",
                  polyline_trajectory.current_segment, polyline_trajectory.current_step);
    }

    polyline_trajectory.trajectory_running = 1;
    multiTimerStart(&mt_polyline_trajectory, TRAJECTORY_UPDATE_PERIOD, app_polyline_task, NULL);
}

/**
 * @brief 停止多点轨迹运动
 */
void app_polyline_stop(void)
{
    polyline_trajectory.trajectory_running = 0;
    multiTimerStop(&mt_polyline_trajectory);
    my_printf(&huart1, "Polyline Stop at Segment%d Step%d\r\n",
              polyline_trajectory.current_segment, polyline_trajectory.current_step);
}

/**
 * @brief 重新开始多点轨迹运动
 */
void app_polyline_restart(void)
{
    polyline_trajectory.current_segment = 0;
    polyline_trajectory.current_step = 0;
    app_polyline_start();
    my_printf(&huart1, "Polyline Restart from Beginning\r\n");
}

/**
 * @brief 复位到多点轨迹中心
 */
void app_polyline_reset_to_center(void)
{
    if (polyline_trajectory.point_count < 2)
    {
        my_printf(&huart1, "Polyline Points Not Set, Cannot Reset\r\n");
        return;
    }

    // 保存当前状态
    if (polyline_trajectory.trajectory_running)
    {
        polyline_trajectory.saved_position = app_polyline_calc_next_point();
        polyline_trajectory.saved_segment = polyline_trajectory.current_segment;
        polyline_trajectory.saved_step = polyline_trajectory.current_step;
    }

    polyline_trajectory.is_resetting = 1;
    polyline_trajectory.is_returning = 0;

    if (!polyline_trajectory.trajectory_running)
    {
        multiTimerStart(&mt_polyline_trajectory, TRAJECTORY_UPDATE_PERIOD, app_polyline_task, NULL);
    }

    trajectory_point_t center = calc_polyline_center();
    app_pid_set_target(center.x, center.y);
    my_printf(&huart1, "Start Reset to Polyline Center(%d,%d)\r\n", center.x, center.y);
}

/**
 * @brief 返回原位置并继续多点轨迹循迹
 */
void app_polyline_return_and_continue(void)
{
    if (!polyline_trajectory.is_resetting)
    {
        my_printf(&huart1, "Polyline Not in Reset State\r\n");
        return;
    }

    polyline_trajectory.is_returning = 1;
    polyline_trajectory.is_resetting = 0;

    // 恢复保存的位置
    polyline_trajectory.current_segment = polyline_trajectory.saved_segment;
    polyline_trajectory.current_step = polyline_trajectory.saved_step;

    app_pid_set_target(polyline_trajectory.saved_position.x, polyline_trajectory.saved_position.y);
    my_printf(&huart1, "Return to Saved Position(%d,%d) Segment%d Step%d\r\n",
              polyline_trajectory.saved_position.x, polyline_trajectory.saved_position.y,
              polyline_trajectory.saved_segment, polyline_trajectory.saved_step);
}

/**
 * @brief 计算多点轨迹中心点
 */
trajectory_point_t calc_polyline_center(void)
{
    trajectory_point_t center = {0, 0};
    if (polyline_trajectory.point_count == 0)
        return center;

    int sum_x = 0, sum_y = 0;
    for (int i = 0; i < polyline_trajectory.point_count; i++)
    {
        sum_x += polyline_trajectory.points[i].x;
        sum_y += polyline_trajectory.points[i].y;
    }

    center.x = sum_x / polyline_trajectory.point_count;
    center.y = sum_y / polyline_trajectory.point_count;
    return center;
}

/**
 * @brief 计算多点轨迹下一个轨迹点（线性插值）
 */
trajectory_point_t app_polyline_calc_next_point(void)
{
    trajectory_point_t next_point = {0, 0};

    if (polyline_trajectory.point_count < 2)
    {
        return next_point; // 点数不足，返回原点
    }

    // 获取当前线段的起点和终点
    trajectory_point_t start = polyline_trajectory.points[polyline_trajectory.current_segment];
    trajectory_point_t end;

    // 处理循环：最后一个点到第一个点
    if (polyline_trajectory.current_segment >= polyline_trajectory.point_count - 1)
    {
        end = polyline_trajectory.points[0]; // 从最后一点回到第一点
    }
    else
    {
        end = polyline_trajectory.points[polyline_trajectory.current_segment + 1];
    }

    // 线性插值计算
    float ratio = (float)polyline_trajectory.current_step / (float)polyline_trajectory.total_steps_per_segment;
    next_point.x = start.x + (int)((end.x - start.x) * ratio);
    next_point.y = start.y + (int)((end.y - start.y) * ratio);

    return next_point;
}

/**
 * @brief 轨迹平滑处理（减少梯形轨迹）
 */
trajectory_point_t app_polyline_smooth_target(trajectory_point_t raw_target)
{
    trajectory_point_t smooth_target;

    // 第一次调用时，直接使用原始目标
    if (polyline_trajectory.last_target.x == 0 && polyline_trajectory.last_target.y == 0)
    {
        polyline_trajectory.last_target = raw_target;
        return raw_target;
    }

    // 使用低通滤波平滑轨迹
    smooth_target.x = (int)(polyline_trajectory.last_target.x * TRAJECTORY_SMOOTH_FACTOR +
                            raw_target.x * (1.0f - TRAJECTORY_SMOOTH_FACTOR));
    smooth_target.y = (int)(polyline_trajectory.last_target.y * TRAJECTORY_SMOOTH_FACTOR +
                            raw_target.y * (1.0f - TRAJECTORY_SMOOTH_FACTOR));

    // 更新上一个目标点
    polyline_trajectory.last_target = smooth_target;

    return smooth_target;
}

/**
 * @brief 多点轨迹任务函数
 */
void app_polyline_task(MultiTimer *timer, void *userData)
{
    // 如果正在复位到中心
    if (polyline_trajectory.is_resetting)
    {
        static uint8_t reset_wait_count = 0;
        reset_wait_count++;

        trajectory_point_t center = calc_polyline_center();
        app_pid_set_target(center.x, center.y);

        if (reset_wait_count >= 20)
        { // 等待1000ms
            reset_wait_count = 0;
            polyline_trajectory.is_resetting = 0;
            my_printf(&huart1, "Reached Polyline Center\r\n");
        }
        else
        {
            my_printf(&huart1, "Resetting to Center... %d/20\r\n", reset_wait_count);
        }
    }
    // 如果正在返回原位置
    else if (polyline_trajectory.is_returning)
    {
        static uint8_t return_wait_count = 0;
        return_wait_count++;

        app_pid_set_target(polyline_trajectory.saved_position.x, polyline_trajectory.saved_position.y);

        if (return_wait_count >= 20)
        { // 等待1000ms
            return_wait_count = 0;
            polyline_trajectory.is_returning = 0;
            polyline_trajectory.trajectory_running = 1; // 恢复轨迹运动
            my_printf(&huart1, "Returned to Saved Position, Resume Polyline\r\n");
        }
        else
        {
            my_printf(&huart1, "Returning... %d/20\r\n", return_wait_count);
        }
    }
    // 如果正在移动到起点
    else if (polyline_trajectory.move_to_start)
    {
        static uint8_t start_wait_count = 0;
        start_wait_count++;

        app_pid_set_target(polyline_trajectory.points[0].x, polyline_trajectory.points[0].y);

        if (start_wait_count >= 20)
        { // 等待1000ms
            start_wait_count = 0;
            polyline_trajectory.move_to_start = 0;
            my_printf(&huart1, "Reached Start Point, Begin Polyline Motion\r\n");
        }
        else
        {
            my_printf(&huart1, "Moving to Start... %d/20\r\n", start_wait_count);
        }
    }
    else
    {
        // 正常轨迹运动
        trajectory_point_t target = app_polyline_calc_next_point();

        // 获取摄像头识别的当前位置（通过外部变量）
        extern int current_x, current_y;
        trajectory_point_t current = {current_x, current_y};

        // 检查是否到达目标点
        if (app_trajectory_check_arrival(target, current))
        {
            my_printf(&huart1, "Arrived at Target: X=%d Y=%d, Current: X=%d Y=%d\r\n",
                      target.x, target.y, current.x, current.y);

            // 更新步数
            polyline_trajectory.current_step++;

            // 检查是否完成当前线段
            if (polyline_trajectory.current_step >= polyline_trajectory.total_steps_per_segment)
            {
                polyline_trajectory.current_step = 0;
                polyline_trajectory.current_segment++;

                // 检查是否完成所有线段，循环回到起点
                if (polyline_trajectory.current_segment >= polyline_trajectory.point_count)
                {
                    polyline_trajectory.current_segment = 0; // 循环到起点
                    my_printf(&huart1, "Polyline Completed, Restart from Beginning\r\n");
                }
                else
                {
                    my_printf(&huart1, "Switch to Segment%d\r\n", polyline_trajectory.current_segment);
                }
            }
        }
        else
        {
            // 未到达目标点，应用轨迹平滑后设置目标
            trajectory_point_t smooth_target = app_polyline_smooth_target(target);
            app_pid_set_target(smooth_target.x, smooth_target.y);
            my_printf(&huart1, "Polyline Target: Segment%d Step%d Raw(%d,%d) Smooth(%d,%d) Current(%d,%d)\r\n",
                      polyline_trajectory.current_segment, polyline_trajectory.current_step,
                      target.x, target.y, smooth_target.x, smooth_target.y, current.x, current.y);
        }
    }

    // 重新启动定时器
    multiTimerStart(&mt_polyline_trajectory, TRAJECTORY_UPDATE_PERIOD, app_polyline_task, NULL);
}
