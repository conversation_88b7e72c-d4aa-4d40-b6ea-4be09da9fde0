#ifndef __APP_HMI_H
#define __APP_HMI_H

#include "mydefine.h"

/* HMI协议定义 */
#define HMI_FRAME_HEADER 0xAA // 帧头
#define HMI_FRAME_TAIL 0x55   // 帧尾
#define HMI_MIN_FRAME_LEN 3   // 最小帧长度：帧头+功能码+帧尾

/* HMI数据结构体 */
typedef struct
{
    uint8_t header;    // 帧头
    uint8_t func_code; // 功能码
    uint8_t tail;      // 帧尾
    uint8_t valid;     // 数据有效标志
} HMI_Frame_t;

/* 函数声明 */
void hmi_task(MultiTimer *timer, void *userData);
void hmi_parse_data(char *buffer);
uint8_t hmi_parse_frame(uint8_t *buffer, uint8_t len, HMI_Frame_t *frame);
void hmi_process_command(HMI_Frame_t *frame);

#endif
